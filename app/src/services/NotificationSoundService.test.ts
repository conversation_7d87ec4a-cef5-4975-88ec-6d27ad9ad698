import { NotificationSoundService } from './NotificationSoundService';

// Mock logger
jest.mock('@utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));

describe('NotificationSoundService', () => {
  beforeEach(() => {
    // Reset to default state before each test
    NotificationSoundService.resetToDefault();
  });

  describe('setCustomSound', () => {
    it('should set custom sound with valid name', () => {
      NotificationSoundService.setCustomSound('test_sound', true);
      const config = NotificationSoundService.getCurrentSoundConfig();
      
      expect(config.soundName).toBe('test_sound');
      expect(config.enabled).toBe(true);
    });

    it('should handle empty sound name by using default', () => {
      NotificationSoundService.setCustomSound('', true);
      const config = NotificationSoundService.getCurrentSoundConfig();
      
      expect(config.soundName).toBe('notification_sound');
      expect(config.enabled).toBe(true);
    });
  });

  describe('resetToDefault', () => {
    it('should reset to default configuration', () => {
      // First set a custom sound
      NotificationSoundService.setCustomSound('custom_sound', false);
      
      // Then reset
      NotificationSoundService.resetToDefault();
      const config = NotificationSoundService.getCurrentSoundConfig();
      
      expect(config.soundName).toBe('notification_sound');
      expect(config.enabled).toBe(true);
    });
  });

  describe('enableCustomSound and disableCustomSound', () => {
    it('should enable custom sound', () => {
      NotificationSoundService.disableCustomSound();
      NotificationSoundService.enableCustomSound();
      
      const config = NotificationSoundService.getCurrentSoundConfig();
      expect(config.enabled).toBe(true);
    });

    it('should disable custom sound', () => {
      NotificationSoundService.enableCustomSound();
      NotificationSoundService.disableCustomSound();
      
      const config = NotificationSoundService.getCurrentSoundConfig();
      expect(config.enabled).toBe(false);
    });
  });

  describe('getSoundNameForNotification', () => {
    it('should return sound name when enabled', () => {
      NotificationSoundService.setCustomSound('test_sound', true);
      const soundName = NotificationSoundService.getSoundNameForNotification();
      
      expect(soundName).toBe('test_sound');
    });

    it('should return null when disabled', () => {
      NotificationSoundService.setCustomSound('test_sound', false);
      const soundName = NotificationSoundService.getSoundNameForNotification();
      
      expect(soundName).toBeNull();
    });
  });

  describe('validateSoundName', () => {
    it('should validate correct sound names', () => {
      expect(NotificationSoundService.validateSoundName('notification_sound')).toBe(true);
      expect(NotificationSoundService.validateSoundName('test-sound')).toBe(true);
      expect(NotificationSoundService.validateSoundName('sound123')).toBe(true);
    });

    it('should reject invalid sound names', () => {
      expect(NotificationSoundService.validateSoundName('')).toBe(false);
      expect(NotificationSoundService.validateSoundName('sound with spaces')).toBe(false);
      expect(NotificationSoundService.validateSoundName('sound@special')).toBe(false);
    });
  });

  describe('getSoundFileRequirements', () => {
    it('should return iOS requirements when platform is iOS', () => {
      const requirements = NotificationSoundService.getSoundFileRequirements();
      
      expect(requirements.platform).toBe('iOS');
      expect(requirements.recommendedFormat).toBe('caf');
      expect(requirements.supportedFormats).toContain('caf');
      expect(requirements.location).toContain('ios');
    });
  });

  describe('getCurrentSoundConfig', () => {
    it('should return a copy of the configuration', () => {
      const config1 = NotificationSoundService.getCurrentSoundConfig();
      const config2 = NotificationSoundService.getCurrentSoundConfig();
      
      expect(config1).toEqual(config2);
      expect(config1).not.toBe(config2); // Should be different objects
    });
  });
});

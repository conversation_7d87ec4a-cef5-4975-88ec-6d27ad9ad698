//
//  NotificationService.swift
//  NotificationServiceExtension
//
//  Created by <PERSON><PERSON><PERSON> on 18/06/25.
//

import UserNotifications
import AirshipServiceExtension

class NotificationService: UNNotificationServiceExtension {

    var contentHandler: ((UNNotificationContent) -> Void)?
    var bestAttemptContent: UNMutableNotificationContent?

    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        self.contentHandler = contentHandler
        bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)

        // Check if this is an Airship notification
        if UANotificationServiceExtension.isAirshipNotification(request) {
            // Let Airship handle the notification (for rich media, analytics, etc.)
            UANotificationServiceExtension.handleNotificationRequest(
                request,
                withContentHandler: contentHandler
            )
        } else {
            // Handle non-Airship notifications
            if let bestAttemptContent = bestAttemptContent {
                // Check for image URL in the notification payload
                if let imageUrlString = request.content.userInfo["image_url"] as? String,
                   let imageUrl = URL(string: imageUrlString) {
                    // Download and attach the image
                    downloadAndAttachImage(from: imageUrl, to: bestAttemptContent) { modifiedContent in
                        contentHandler(modifiedContent)
                    }
                } else {
                    contentHandler(bestAttemptContent)
                }
            }
        }
    }

    private func downloadAndAttachImage(from url: URL, to content: UNMutableNotificationContent, completion: @escaping (UNNotificationContent) -> Void) {
        let task = URLSession.shared.dataTask(with: url) { (data, response, error) in
            guard let data = data, error == nil else {
                completion(content)
                return
            }
            
            // Create a temporary file to store the image
            let tempDir = FileManager.default.temporaryDirectory
            let fileName = UUID().uuidString + ".jpg"
            let fileURL = tempDir.appendingPathComponent(fileName)
            
            do {
                try data.write(to: fileURL)
                let attachment = try UNNotificationAttachment(identifier: "image", url: fileURL, options: nil)
                content.attachments = [attachment]
                completion(content)
            } catch {
                print("Error creating notification attachment: \(error)")
                completion(content)
            }
        }
        task.resume()
    }

    override func serviceExtensionTimeWillExpire() {
        // Called just before the extension will be terminated by the system.
        // Use this as an opportunity to deliver your "best attempt" at modified content, otherwise the original push payload will be used.
        if let contentHandler = contentHandler, let bestAttemptContent = bestAttemptContent {
            contentHandler(bestAttemptContent)
        }
    }

}

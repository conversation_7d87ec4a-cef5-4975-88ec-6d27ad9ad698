import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, Platform } from 'react-native';
import { AirshipAnalyticsService } from '@analytics/handlers/airship';
import { NotificationSoundService } from '../../services/NotificationSoundService';
import { logger } from '@utils/logger';

interface NotificationSoundExampleProps {
  onClose?: () => void;
}

const NotificationSoundExample: React.FC<NotificationSoundExampleProps> = ({ onClose }) => {
  const [currentConfig, setCurrentConfig] = useState(NotificationSoundService.getCurrentSoundConfig());
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Log current configuration on mount
    AirshipAnalyticsService.logNotificationSoundConfiguration();
  }, []);

  const updateConfig = () => {
    setCurrentConfig(NotificationSoundService.getCurrentSoundConfig());
  };

  const handleSetCustomSound = async () => {
    setIsLoading(true);
    try {
      // Configure custom notification sound
      AirshipAnalyticsService.configureNotificationSound('notification_sound', true);
      updateConfig();
      
      Alert.alert(
        'Custom Sound Configured',
        `Custom notification sound has been set to: ${currentConfig.soundName}\n\nMake sure you have placed the sound file in the correct location:\n\n${Platform.OS === 'ios' ? 'iOS: app/ios/bppulse/Sounds/notification_sound.caf' : 'Android: app/android/app/src/main/res/raw/notification_sound.mp3'}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      logger.error('Error setting custom sound:', error);
      Alert.alert('Error', 'Failed to configure custom sound');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetToDefault = () => {
    setIsLoading(true);
    try {
      AirshipAnalyticsService.resetNotificationSoundToDefault();
      updateConfig();
      Alert.alert('Reset Complete', 'Notification sound has been reset to default');
    } catch (error) {
      logger.error('Error resetting sound:', error);
      Alert.alert('Error', 'Failed to reset sound');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleEnabled = () => {
    setIsLoading(true);
    try {
      if (currentConfig.enabled) {
        NotificationSoundService.disableCustomSound();
      } else {
        NotificationSoundService.enableCustomSound();
      }
      updateConfig();
    } catch (error) {
      logger.error('Error toggling sound:', error);
      Alert.alert('Error', 'Failed to toggle sound setting');
    } finally {
      setIsLoading(false);
    }
  };

  const showRequirements = () => {
    const requirements = NotificationSoundService.getSoundFileRequirements();
    Alert.alert(
      `${requirements.platform} Sound Requirements`,
      `Supported formats: ${requirements.supportedFormats.join(', ')}\n` +
      `Recommended: ${requirements.recommendedFormat}\n` +
      `Max size: ${requirements.maxSize}\n` +
      `Location: ${requirements.location}`,
      [{ text: 'OK' }]
    );
  };

  return (
    <View style={{ padding: 20, backgroundColor: 'white', borderRadius: 10, margin: 20 }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
        Custom Notification Sound Configuration
      </Text>
      
      <View style={{ marginBottom: 15 }}>
        <Text style={{ fontSize: 14, color: '#666' }}>Current Configuration:</Text>
        <Text style={{ fontSize: 16, fontWeight: '500' }}>
          Sound: {currentConfig.soundName}
        </Text>
        <Text style={{ fontSize: 16, fontWeight: '500' }}>
          Enabled: {currentConfig.enabled ? 'Yes' : 'No'}
        </Text>
        <Text style={{ fontSize: 14, color: '#666', marginTop: 5 }}>
          Platform: {Platform.OS}
        </Text>
      </View>

      <TouchableOpacity
        style={{
          backgroundColor: '#007AFF',
          padding: 12,
          borderRadius: 8,
          marginBottom: 10,
          opacity: isLoading ? 0.6 : 1,
        }}
        onPress={handleSetCustomSound}
        disabled={isLoading}
      >
        <Text style={{ color: 'white', textAlign: 'center', fontWeight: '500' }}>
          Set Custom Sound
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={{
          backgroundColor: currentConfig.enabled ? '#FF3B30' : '#34C759',
          padding: 12,
          borderRadius: 8,
          marginBottom: 10,
          opacity: isLoading ? 0.6 : 1,
        }}
        onPress={handleToggleEnabled}
        disabled={isLoading}
      >
        <Text style={{ color: 'white', textAlign: 'center', fontWeight: '500' }}>
          {currentConfig.enabled ? 'Disable Custom Sound' : 'Enable Custom Sound'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={{
          backgroundColor: '#FF9500',
          padding: 12,
          borderRadius: 8,
          marginBottom: 10,
          opacity: isLoading ? 0.6 : 1,
        }}
        onPress={handleResetToDefault}
        disabled={isLoading}
      >
        <Text style={{ color: 'white', textAlign: 'center', fontWeight: '500' }}>
          Reset to Default
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={{
          backgroundColor: '#8E8E93',
          padding: 12,
          borderRadius: 8,
          marginBottom: 10,
        }}
        onPress={showRequirements}
      >
        <Text style={{ color: 'white', textAlign: 'center', fontWeight: '500' }}>
          Show File Requirements
        </Text>
      </TouchableOpacity>

      {onClose && (
        <TouchableOpacity
          style={{
            backgroundColor: '#666',
            padding: 12,
            borderRadius: 8,
          }}
          onPress={onClose}
        >
          <Text style={{ color: 'white', textAlign: 'center', fontWeight: '500' }}>
            Close
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default NotificationSoundExample;

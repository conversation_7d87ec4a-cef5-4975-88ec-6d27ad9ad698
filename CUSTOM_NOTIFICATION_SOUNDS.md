# Custom Notification Sounds Implementation

This document explains how to implement and use custom notification sounds for push notifications in the BP Pulse mobile app using the existing Airship push notification system.

## Overview

The implementation provides custom notification sounds for both Android and iOS platforms without requiring additional npm packages. It uses the existing Airship SDK configuration to handle custom sounds.

## Features

- ✅ Custom notification sounds for both Android and iOS
- ✅ Dynamic sound configuration at runtime
- ✅ Fallback to system default sounds
- ✅ Sound validation and error handling
- ✅ Platform-specific file format support
- ✅ No additional npm packages required

## File Structure

```
app/
├── android/app/src/main/res/raw/          # Android sound files
│   ├── notification_sound.mp3             # Your custom sound file
│   └── README.md                          # Instructions
├── ios/bppulse/Sounds/                    # iOS sound files
│   ├── notification_sound.caf             # Your custom sound file
│   └── README.md                          # Instructions
├── src/
│   ├── services/
│   │   └── NotificationSoundService.ts    # Sound management service
│   ├── analytics/handlers/
│   │   └── airship.ts                     # Updated Airship configuration
│   └── components/
│       └── NotificationSoundExample/      # Example component
```

## Implementation Steps

### 1. Add Your Sound Files

#### Android
1. Place your sound file in: `app/android/app/src/main/res/raw/`
2. Supported formats: MP3, WAV, OGG
3. Recommended: MP3 format
4. File name: `notification_sound.mp3` (or your custom name)

#### iOS
1. Place your sound file in: `app/ios/bppulse/Sounds/`
2. Supported formats: CAF, WAV, AIFF, MP3
3. Recommended: CAF format
4. File name: `notification_sound.caf` (or your custom name)

**Converting to CAF format (iOS):**
```bash
afconvert -f caff -d LEI16 input.wav notification_sound.caf
```

### 2. Add Sound File to iOS Project

For iOS, you must add the sound file to the Xcode project:

1. Open `app/ios/bppulse.xcworkspace` in Xcode
2. Right-click on the `bppulse` folder in the project navigator
3. Select "Add Files to 'bppulse'"
4. Navigate to `app/ios/bppulse/Sounds/` and select your sound file
5. Ensure "Add to target" is checked for `bppulse`
6. Click "Add"

### 3. Configure Custom Sound in Your App

```typescript
import { AirshipAnalyticsService } from '@analytics/handlers/airship';

// Set custom notification sound
AirshipAnalyticsService.configureNotificationSound('notification_sound', true);

// Reset to default sound
AirshipAnalyticsService.resetNotificationSoundToDefault();

// Get current configuration
const config = AirshipAnalyticsService.getNotificationSoundConfig();
console.log('Current sound:', config.soundName, 'Enabled:', config.enabled);
```

### 4. Using the NotificationSoundService Directly

```typescript
import { NotificationSoundService } from '@services/NotificationSoundService';

// Set custom sound
NotificationSoundService.setCustomSound('my_custom_sound', true);

// Disable custom sound (use system default)
NotificationSoundService.disableCustomSound();

// Enable custom sound
NotificationSoundService.enableCustomSound();

// Get platform requirements
const requirements = NotificationSoundService.getSoundFileRequirements();
console.log('Platform:', requirements.platform);
console.log('Supported formats:', requirements.supportedFormats);
```

## API Reference

### AirshipAnalyticsService Methods

#### `configureNotificationSound(soundName: string, enabled: boolean = true)`
Configure a custom notification sound.

**Parameters:**
- `soundName`: Name of the sound file (without extension)
- `enabled`: Whether to enable the custom sound

#### `resetNotificationSoundToDefault()`
Reset the notification sound to the default.

#### `getNotificationSoundConfig()`
Get the current notification sound configuration.

#### `logNotificationSoundConfiguration()`
Log the current configuration for debugging.

### NotificationSoundService Methods

#### `setCustomSound(soundName: string, enabled: boolean = true)`
Set a custom notification sound.

#### `resetToDefault()`
Reset to the default notification sound.

#### `disableCustomSound()` / `enableCustomSound()`
Disable or enable custom notification sounds.

#### `getSoundNameForNotification(): string | null`
Get the sound name to use for notifications (null = system default).

#### `validateSoundName(soundName: string): boolean`
Validate if a sound name is valid.

#### `getSoundFileRequirements()`
Get platform-specific file requirements.

## Sound File Requirements

### Android
- **Location**: `app/android/app/src/main/res/raw/`
- **Formats**: MP3, WAV, OGG
- **Recommended**: MP3
- **Max Size**: 1MB
- **Duration**: 2-5 seconds

### iOS
- **Location**: `app/ios/bppulse/Sounds/` (must be added to Xcode project)
- **Formats**: CAF, WAV, AIFF, MP3
- **Recommended**: CAF
- **Max Size**: 1MB
- **Duration**: 2-5 seconds

## Example Usage

See the `NotificationSoundExample` component for a complete implementation example:

```typescript
import NotificationSoundExample from '@components/NotificationSoundExample';

// Use in your component
<NotificationSoundExample onClose={() => setShowExample(false)} />
```

## Testing

1. Configure a custom sound using the API
2. Send a test push notification through Airship dashboard
3. Verify the custom sound plays on notification receipt
4. Test on both Android and iOS devices

## Troubleshooting

### Sound Not Playing
1. Verify the sound file is in the correct location
2. Check file format compatibility
3. Ensure file size is under 1MB
4. For iOS: Verify the file is added to the Xcode project
5. Check device volume and notification settings

### Configuration Issues
1. Check logs for validation errors
2. Verify sound name doesn't contain invalid characters
3. Ensure the sound file exists before configuration

### Platform-Specific Issues

**Android:**
- File must be in `res/raw/` directory
- File name should be lowercase with no spaces
- Use underscores instead of spaces or hyphens

**iOS:**
- File must be added to Xcode project bundle
- CAF format provides best compatibility
- Check iOS notification permissions

## Notes

- Custom sounds only work for push notifications, not for other app sounds
- The implementation respects user notification preferences
- Sounds are configured at the Airship SDK level during initialization
- Changes to sound configuration may require app restart to take full effect
- Always test on physical devices as simulators may not play custom sounds correctly

# Custom Notification Sounds for iOS

## Instructions

1. Place your custom notification sound file in this directory
2. The file should be named `notification_sound.caf` (recommended) or `notification_sound.wav`
3. iOS supports: CAF, WAV, AIFF, and MP3 formats
4. Recommended format: CAF (Core Audio Format) for best compatibility
5. Recommended file size: Under 1MB for optimal performance
6. Recommended duration: 2-5 seconds
7. The sound file must be added to the Xcode project bundle

## Example

Place your sound file as:
- `notification_sound.caf` (recommended)
- `notification_sound.wav` (alternative)

## Converting to CAF format

You can convert your audio file to CAF format using the following command:
```bash
afconvert -f caff -d LEI16 input.wav notification_sound.caf
```

## Adding to Xcode Project

After placing the sound file here, you need to:
1. Open the Xcode project
2. Right-click on the bppulse folder in Xcode
3. Select "Add Files to 'bppulse'"
4. Select your sound file
5. Make sure "Add to target" is checked for bppulse

## Note

This README.md file should be removed in production and replaced with your actual sound file.
